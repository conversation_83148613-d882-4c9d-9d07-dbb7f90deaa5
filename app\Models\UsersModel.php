<?php

namespace App\Models;

use CodeIgniter\Model;

class UsersModel extends Model
{
    protected $table            = 'users';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'email',
        'password_hash',
        'first_name',
        'last_name',
        'role',
        'status'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'email' => [
            'label' => 'Email',
            'rules' => 'required|valid_email|is_unique[users.email,id,{id}]',
            'errors' => [
                'required' => 'Email is required.',
                'valid_email' => 'Please enter a valid email address.',
                'is_unique' => 'This email is already registered.'
            ]
        ],
        'password_hash' => [
            'label' => 'Password',
            'rules' => 'required|min_length[6]',
            'errors' => [
                'required' => 'Password is required.',
                'min_length' => 'Password must be at least 6 characters long.'
            ]
        ],
        'first_name' => [
            'label' => 'First Name',
            'rules' => 'required|max_length[100]',
            'errors' => [
                'required' => 'First name is required.',
                'max_length' => 'First name cannot exceed 100 characters.'
            ]
        ],
        'last_name' => [
            'label' => 'Last Name',
            'rules' => 'required|max_length[100]',
            'errors' => [
                'required' => 'Last name is required.',
                'max_length' => 'Last name cannot exceed 100 characters.'
            ]
        ],
        'role' => [
            'label' => 'Role',
            'rules' => 'required|in_list[admin,user]',
            'errors' => [
                'required' => 'Role is required.',
                'in_list' => 'Role must be either admin or user.'
            ]
        ],
        'status' => [
            'label' => 'Status',
            'rules' => 'required|in_list[active,inactive]',
            'errors' => [
                'required' => 'Status is required.',
                'in_list' => 'Status must be either active or inactive.'
            ]
        ]
    ];

    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['hashPassword'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['hashPassword'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Hash password before saving to database
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password_hash'])) {
            $data['data']['password_hash'] = password_hash($data['data']['password_hash'], PASSWORD_DEFAULT);
        }
        
        return $data;
    }

    /**
     * Verify user credentials for login
     */
    public function verifyCredentials(string $email, string $password): array|false
    {
        $user = $this->where('email', $email)
                     ->where('status', 'active')
                     ->first();

        if ($user && password_verify($password, $user['password_hash'])) {
            // Remove password hash from returned data for security
            unset($user['password_hash']);
            return $user;
        }

        return false;
    }

    /**
     * Get user by email
     */
    public function getUserByEmail(string $email): array|null
    {
        $user = $this->where('email', $email)->first();
        
        if ($user) {
            // Remove password hash from returned data for security
            unset($user['password_hash']);
        }
        
        return $user;
    }

    /**
     * Get user by ID (without password hash)
     */
    public function getUserById(int $id): array|null
    {
        $user = $this->find($id);
        
        if ($user) {
            // Remove password hash from returned data for security
            unset($user['password_hash']);
        }
        
        return $user;
    }



    /**
     * Check if user is admin
     */
    public function isAdmin(int $userId): bool
    {
        $user = $this->find($userId);
        return $user && $user['role'] === 'admin';
    }

    /**
     * Get active users count
     */
    public function getActiveUsersCount(): int
    {
        return $this->where('status', 'active')->countAllResults();
    }

    /**
     * Create new user with validation
     */
    public function createUser(array $userData): int|false
    {
        // Set default values if not provided
        $userData['role'] = $userData['role'] ?? 'user';
        $userData['status'] = $userData['status'] ?? 'active';
        
        if ($this->insert($userData)) {
            return $this->getInsertID();
        }
        
        return false;
    }
}
