<?php

namespace App\Controllers;

use App\Models\UsersModel;

class Dashboard extends BaseController
{
    protected $userModel;
    protected $session;

    public function __construct()
    {
        $this->userModel = new UsersModel();
        $this->session = session();
        helper(['form', 'url']);
    }

    /**
     * Dashboard home page
     */
    public function index()
    {
        // Get current user data
        $userId = $this->session->get('user_id');
        $user = $this->userModel->getUserById($userId);

        // Get some basic statistics for dashboard
        $stats = [
            'total_users' => $this->userModel->getActiveUsersCount(),
            'user_role' => $this->session->get('role'),
            'login_time' => $this->session->get('login_time') ?? date('Y-m-d H:i:s')
        ];

        $data = [
            'title' => 'Dashboard - Dakoii Accounts',
            'user' => $user,
            'stats' => $stats,
            'current_page' => 'dashboard'
        ];

        return view('dashboard/dashboard_index_new', $data);
    }

    /**
     * User profile page
     */
    public function profile()
    {
        $userId = $this->session->get('user_id');
        $user = $this->userModel->getUserById($userId);

        $data = [
            'title' => 'Profile - Dakoii Accounts',
            'user' => $user,
            'current_page' => 'profile'
        ];

        return view('dashboard/dashboard_profile_new', $data);
    }

    /**
     * Update user profile
     */
    public function updateProfile()
    {
        $userId = $this->session->get('user_id');

        // Validation rules for profile update
        $rules = [
            'first_name' => [
                'label' => 'First Name',
                'rules' => 'required|max_length[100]',
                'errors' => [
                    'required' => 'First name is required.',
                    'max_length' => 'First name cannot exceed 100 characters.'
                ]
            ],
            'last_name' => [
                'label' => 'Last Name',
                'rules' => 'required|max_length[100]',
                'errors' => [
                    'required' => 'Last name is required.',
                    'max_length' => 'Last name cannot exceed 100 characters.'
                ]
            ],
            'email' => [
                'label' => 'Email',
                'rules' => "required|valid_email|is_unique[users.email,id,{$userId}]",
                'errors' => [
                    'required' => 'Email is required.',
                    'valid_email' => 'Please enter a valid email address.',
                    'is_unique' => 'This email is already registered.'
                ]
            ]
        ];

        if (!$this->validate($rules)) {
            $user = $this->userModel->getUserById($userId);
            $data = [
                'title' => 'Profile - Dakoii Accounts',
                'user' => $user,
                'current_page' => 'profile',
                'validation' => $this->validator
            ];
            return view('dashboard/dashboard_profile_new', $data);
        }

        // Update user data
        $updateData = [
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'email' => $this->request->getPost('email')
        ];

        if ($this->userModel->update($userId, $updateData)) {
            // Update session data
            $this->session->set([
                'first_name' => $updateData['first_name'],
                'last_name' => $updateData['last_name'],
                'email' => $updateData['email']
            ]);

            $this->session->setFlashdata('success', 'Profile updated successfully!');
        } else {
            $this->session->setFlashdata('error', 'Failed to update profile. Please try again.');
        }

        return redirect()->to('/dashboard/profile');
    }

    /**
     * Change password page
     */
    public function changePassword()
    {
        $data = [
            'title' => 'Change Password - Dakoii Accounts',
            'current_page' => 'change_password'
        ];

        return view('dashboard/dashboard_change_password_new', $data);
    }

    /**
     * Process password change
     */
    public function updatePassword()
    {
        $userId = $this->session->get('user_id');

        // Validation rules for password change
        $rules = [
            'current_password' => [
                'label' => 'Current Password',
                'rules' => 'required',
                'errors' => [
                    'required' => 'Current password is required.'
                ]
            ],
            'new_password' => [
                'label' => 'New Password',
                'rules' => 'required|min_length[6]',
                'errors' => [
                    'required' => 'New password is required.',
                    'min_length' => 'New password must be at least 6 characters long.'
                ]
            ],
            'confirm_password' => [
                'label' => 'Confirm Password',
                'rules' => 'required|matches[new_password]',
                'errors' => [
                    'required' => 'Please confirm your new password.',
                    'matches' => 'Password confirmation does not match.'
                ]
            ]
        ];

        if (!$this->validate($rules)) {
            $data = [
                'title' => 'Change Password - Dakoii Accounts',
                'current_page' => 'change_password',
                'validation' => $this->validator
            ];
            return view('dashboard/dashboard_change_password_new', $data);
        }

        // Verify current password
        $currentPassword = $this->request->getPost('current_password');
        $user = $this->userModel->find($userId);

        if (!password_verify($currentPassword, $user['password_hash'])) {
            $this->session->setFlashdata('error', 'Current password is incorrect.');
            return redirect()->to('/dashboard/change-password');
        }

        // Update password
        $newPassword = $this->request->getPost('new_password');
        $updateData = ['password_hash' => $newPassword]; // Will be hashed by model callback

        if ($this->userModel->update($userId, $updateData)) {
            $this->session->setFlashdata('success', 'Password changed successfully!');
        } else {
            $this->session->setFlashdata('error', 'Failed to change password. Please try again.');
        }

        return redirect()->to('/dashboard/change-password');
    }

    /**
     * Admin users management (only for admin users)
     */
    public function users()
    {
        // Check if user is admin
        if ($this->session->get('role') !== 'admin') {
            $this->session->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to('/dashboard');
        }

        // Get all users
        $users = $this->userModel->findAll();

        $data = [
            'title' => 'Users Management - Dakoii Accounts',
            'users' => $users,
            'current_page' => 'users'
        ];

        return view('dashboard/dashboard_users_new', $data);
    }

    /**
     * Show create user form (Admin only)
     */
    public function createUser()
    {
        // Check if user is admin
        if ($this->session->get('role') !== 'admin') {
            $this->session->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to('/dashboard');
        }

        $data = [
            'title' => 'Create New User - Dakoii Accounts',
            'user' => [],
            'validation' => \Config\Services::validation(),
            'current_page' => 'users'
        ];

        return view('dashboard/user_create', $data);
    }

    /**
     * Store new user (Admin only) - RESTful approach
     */
    public function storeUser()
    {
        // Check if user is admin
        if ($this->session->get('role') !== 'admin') {
            $this->session->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to('/dashboard');
        }

        // Ensure this method only handles POST requests
        if (!$this->request->is('post')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }

        $data = $this->request->getPost();

        // Create user using the model's createUser method
        $userId = $this->userModel->createUser($data);

        if ($userId) {
            $this->session->setFlashdata('success', 'User created successfully.');
            return redirect()->to(base_url('dashboard/users'));
        } else {
            $this->session->setFlashdata('error', 'Failed to create user. Please check the form for errors.');
            $this->session->setFlashdata('validation', $this->userModel->errors());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show user details (Admin only)
     */
    public function showUser(int $id)
    {
        // Check if user is admin
        if ($this->session->get('role') !== 'admin') {
            $this->session->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to('/dashboard');
        }

        $user = $this->userModel->getUserById($id);

        if (!$user) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('User not found');
        }

        $data = [
            'title' => 'User Details - ' . $user['first_name'] . ' ' . $user['last_name'],
            'user' => $user,
            'current_page' => 'users'
        ];

        return view('dashboard/user_view', $data);
    }

    /**
     * Show edit user form (Admin only)
     */
    public function editUser(int $id)
    {
        // Check if user is admin
        if ($this->session->get('role') !== 'admin') {
            $this->session->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to('/dashboard');
        }

        $user = $this->userModel->find($id);

        if (!$user) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('User not found');
        }

        $data = [
            'title' => 'Edit User - ' . $user['first_name'] . ' ' . $user['last_name'],
            'user' => $user,
            'validation' => \Config\Services::validation(),
            'current_page' => 'users'
        ];

        return view('dashboard/user_edit', $data);
    }

    /**
     * Update user (Admin only) - RESTful approach
     */
    public function updateUser(int $id)
    {
        // Check if user is admin
        if ($this->session->get('role') !== 'admin') {
            $this->session->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to('/dashboard');
        }

        // Ensure this method only handles PUT/PATCH requests
        if (!$this->request->is('put') && !$this->request->is('patch')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }

        $user = $this->userModel->find($id);

        if (!$user) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('User not found');
        }

        $data = $this->request->getPost();

        // Remove password if empty (don't update password)
        if (empty($data['password_hash'])) {
            unset($data['password_hash']);
        }

        if ($this->userModel->update($id, $data)) {
            $this->session->setFlashdata('success', 'User updated successfully.');
            return redirect()->to(base_url('dashboard/users/' . $id));
        } else {
            $this->session->setFlashdata('error', 'Failed to update user. Please check the form for errors.');
            $this->session->setFlashdata('validation', $this->userModel->errors());
            return redirect()->back()->withInput();
        }
    }

    /**
     * Delete user (Admin only) - RESTful approach
     */
    public function destroyUser(int $id)
    {
        // Check if user is admin
        if ($this->session->get('role') !== 'admin') {
            $this->session->setFlashdata('error', 'Access denied. Admin privileges required.');
            return redirect()->to('/dashboard');
        }

        // Ensure this method only handles DELETE requests
        if (!$this->request->is('delete')) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Method not allowed');
        }

        $user = $this->userModel->find($id);

        if (!$user) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('User not found');
        }

        // Prevent deleting current user
        if ($id == $this->session->get('user_id')) {
            $this->session->setFlashdata('error', 'You cannot delete your own account.');
            return redirect()->to(base_url('dashboard/users'));
        }

        if ($this->userModel->delete($id)) {
            $this->session->setFlashdata('success', 'User deleted successfully.');
        } else {
            $this->session->setFlashdata('error', 'Failed to delete user.');
        }

        return redirect()->to(base_url('dashboard/users'));
    }
}
