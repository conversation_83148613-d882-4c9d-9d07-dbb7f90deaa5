<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <h1 class="page-title">Create New User</h1>
    <p class="page-subtitle">Add a new user to the system</p>
</div>

<!-- User Form -->
<form method="POST" action="<?= base_url('dashboard/users') ?>" id="userForm">
    <?= csrf_field() ?>
    
    <div class="row">
        <!-- Left Column -->
        <div class="col-md-8">
            <!-- Basic Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">User Information</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" name="first_name" id="first_name" class="form-control" 
                                       value="<?= old('first_name') ?>" required>
                                <?php if ($validation->hasError('first_name')): ?>
                                    <div class="text-danger small"><?= $validation->getError('first_name') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" name="last_name" id="last_name" class="form-control" 
                                       value="<?= old('last_name') ?>" required>
                                <?php if ($validation->hasError('last_name')): ?>
                                    <div class="text-danger small"><?= $validation->getError('last_name') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                        <input type="email" name="email" id="email" class="form-control" 
                               value="<?= old('email') ?>" required>
                        <?php if ($validation->hasError('email')): ?>
                            <div class="text-danger small"><?= $validation->getError('email') ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password_hash" class="form-label">Password <span class="text-danger">*</span></label>
                        <input type="password" name="password_hash" id="password_hash" class="form-control" 
                               required minlength="6">
                        <div class="form-text">Password must be at least 6 characters long.</div>
                        <?php if ($validation->hasError('password_hash')): ?>
                            <div class="text-danger small"><?= $validation->getError('password_hash') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-md-4">
            <!-- User Settings -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">User Settings</h5>
                </div>
                <div class="admin-card-body">
                    <div class="mb-3">
                        <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                        <select name="role" id="role" class="form-select" required>
                            <option value="">Select Role</option>
                            <option value="user" <?= old('role') === 'user' ? 'selected' : '' ?>>User</option>
                            <option value="admin" <?= old('role') === 'admin' ? 'selected' : '' ?>>Admin</option>
                        </select>
                        <?php if ($validation->hasError('role')): ?>
                            <div class="text-danger small"><?= $validation->getError('role') ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" id="status" class="form-select" required>
                            <option value="">Select Status</option>
                            <option value="active" <?= old('status') === 'active' ? 'selected' : 'selected' ?>>Active</option>
                            <option value="inactive" <?= old('status') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                        <?php if ($validation->hasError('status')): ?>
                            <div class="text-danger small"><?= $validation->getError('status') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="admin-card">
                <div class="admin-card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Create User
                        </button>
                        <a href="<?= base_url('dashboard/users') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>
