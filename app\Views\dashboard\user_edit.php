<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="page-header">
    <h1 class="page-title">Edit User</h1>
    <p class="page-subtitle">Update user information and settings</p>
</div>

<!-- User Form -->
<form method="POST" action="<?= base_url('dashboard/users/' . $user['id']) ?>" id="userForm">
    <?= csrf_field() ?>
    <input type="hidden" name="_method" value="PUT">
    
    <div class="row">
        <!-- Left Column -->
        <div class="col-md-8">
            <!-- Basic Information -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">User Information</h5>
                </div>
                <div class="admin-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" name="first_name" id="first_name" class="form-control" 
                                       value="<?= old('first_name', $user['first_name']) ?>" required>
                                <?php if ($validation->hasError('first_name')): ?>
                                    <div class="text-danger small"><?= $validation->getError('first_name') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" name="last_name" id="last_name" class="form-control" 
                                       value="<?= old('last_name', $user['last_name']) ?>" required>
                                <?php if ($validation->hasError('last_name')): ?>
                                    <div class="text-danger small"><?= $validation->getError('last_name') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                        <input type="email" name="email" id="email" class="form-control" 
                               value="<?= old('email', $user['email']) ?>" required>
                        <?php if ($validation->hasError('email')): ?>
                            <div class="text-danger small"><?= $validation->getError('email') ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password_hash" class="form-label">New Password</label>
                        <input type="password" name="password_hash" id="password_hash" class="form-control" 
                               minlength="6">
                        <div class="form-text">Leave blank to keep current password. Minimum 6 characters if changing.</div>
                        <?php if ($validation->hasError('password_hash')): ?>
                            <div class="text-danger small"><?= $validation->getError('password_hash') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-md-4">
            <!-- User Settings -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">User Settings</h5>
                </div>
                <div class="admin-card-body">
                    <div class="mb-3">
                        <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                        <select name="role" id="role" class="form-select" required>
                            <option value="">Select Role</option>
                            <option value="user" <?= old('role', $user['role']) === 'user' ? 'selected' : '' ?>>User</option>
                            <option value="admin" <?= old('role', $user['role']) === 'admin' ? 'selected' : '' ?>>Admin</option>
                        </select>
                        <?php if ($validation->hasError('role')): ?>
                            <div class="text-danger small"><?= $validation->getError('role') ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" id="status" class="form-select" required>
                            <option value="">Select Status</option>
                            <option value="active" <?= old('status', $user['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= old('status', $user['status']) === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                        <?php if ($validation->hasError('status')): ?>
                            <div class="text-danger small"><?= $validation->getError('status') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- User Info -->
            <div class="admin-card mb-4">
                <div class="admin-card-header">
                    <h5 class="admin-card-title">User Info</h5>
                </div>
                <div class="admin-card-body">
                    <small class="text-muted">
                        <strong>Created:</strong><br>
                        <?= date('M d, Y \a\t g:i A', strtotime($user['created_at'])) ?>
                    </small><br><br>
                    <small class="text-muted">
                        <strong>Last Updated:</strong><br>
                        <?= date('M d, Y \a\t g:i A', strtotime($user['updated_at'])) ?>
                    </small><br><br>
                    <small class="text-muted">
                        <strong>User ID:</strong> <?= $user['id'] ?>
                    </small>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="admin-card">
                <div class="admin-card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Update User
                        </button>
                        <a href="<?= base_url('dashboard/users/' . $user['id']) ?>" class="btn btn-outline-info">
                            <i class="fas fa-eye"></i>
                            View User
                        </a>
                        <a href="<?= base_url('dashboard/users') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>
